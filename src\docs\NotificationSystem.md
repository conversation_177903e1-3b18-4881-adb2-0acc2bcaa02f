# Hệ thống Thông báo Thống nhất (Unified Notification System)

## Tổng quan

Hệ thống thông báo thống nhất cung cấp một cách duy nhất để hiển thị tất cả các loại thông báo trong ứng dụng, bao gồm:

- **Toast notifications**: Thông báo ngắn gọn, tự động biến mất
- **Modal confirmations**: Hộp tho<PERSON>i x<PERSON>h<PERSON>, thông báo
- **Loading states**: Trạng thái loading toàn cục
- **Error handling**: <PERSON>ử lý lỗi API và validation thống nhất

## Cài đặt

Hệ thống đã được tích hợp sẵn trong `NotificationProvider` và có thể sử dụng thông qua hook `useNotification`.

## Cách sử dụng cơ bản

### 1. Import hook

```javascript
import useNotification from '../hooks/useNotification';

const MyComponent = () => {
    const notification = useNotification();
    
    // Sử dụng các methods của notification
};
```

### 2. Toast Notifications

```javascript
// Thông báo thành công
notification.success('Lưu dữ liệu thành công!');

// Thông báo lỗi
notification.error('Có lỗi xảy ra khi lưu dữ liệu!');

// Thông báo cảnh báo
notification.warning('Hãy kiểm tra lại thông tin!');

// Thông báo thông tin
notification.info('Dữ liệu đã được cập nhật.');

// Toast tùy chỉnh
notification.showToast('Thông báo tùy chỉnh', 'info', 5000);
```

### 3. Modal Confirmations

```javascript
// Alert đơn giản
notification.alert(
    'Thông báo',
    'Đây là một thông báo quan trọng.',
    () => console.log('User đã đóng alert')
);

// Confirmation dialog
notification.confirm(
    'Xác nhận',
    'Bạn có chắc chắn muốn thực hiện hành động này?',
    () => console.log('User xác nhận'),
    () => console.log('User hủy bỏ')
);

// Delete confirmation
notification.confirmDelete(
    'Xóa người dùng',
    'Bạn có chắc chắn muốn xóa người dùng này? Hành động này không thể hoàn tác.',
    () => console.log('Xóa thành công'),
    () => console.log('Hủy xóa')
);

// Success modal
notification.showSuccess(
    'Thành công',
    'Dữ liệu đã được lưu thành công!',
    () => console.log('User đã xem thông báo thành công')
);

// Error modal
notification.showError(
    'Lỗi',
    'Không thể kết nối đến server.',
    () => console.log('User đã xem thông báo lỗi')
);
```

### 4. Loading States

```javascript
// Hiển thị loading
notification.showLoading('Đang tải dữ liệu...');

// Ẩn loading
notification.hideLoading();

// Ví dụ sử dụng trong async function
const fetchData = async () => {
    try {
        notification.showLoading('Đang tải...');
        const data = await api.getData();
        // Process data
    } catch (error) {
        notification.handleApiError(error);
    } finally {
        notification.hideLoading();
    }
};
```

### 5. Error Handling

```javascript
// Xử lý lỗi API tự động
try {
    await api.submitData(data);
} catch (error) {
    notification.handleApiError(error);
    // Sẽ tự động hiển thị thông báo lỗi phù hợp
}

// Xử lý lỗi validation
const validationErrors = [
    'Email không đúng định dạng',
    'Mật khẩu phải có ít nhất 6 ký tự'
];
notification.handleValidationErrors(validationErrors);
```

### 6. Form Submission với Error Handling

```javascript
const handleSubmit = async () => {
    const submitFunction = async () => {
        const response = await api.submitForm(formData);
        return response.data;
    };

    try {
        await notification.handleFormSubmission(submitFunction, {
            loadingMessage: 'Đang lưu dữ liệu...',
            successMessage: 'Lưu thành công!',
            successTitle: 'Hoàn tất',
            showSuccessModal: true, // Hiển thị modal thay vì toast
            onSuccess: (result) => {
                // Xử lý sau khi thành công
                resetForm();
                navigate('/success');
            },
            onError: (error, errorMessage) => {
                // Xử lý khi có lỗi
                console.error('Submission failed:', error);
            }
        });
    } catch (error) {
        // Lỗi đã được xử lý tự động
    }
};
```

## API Reference

### Toast Methods

| Method | Tham số | Mô tả |
|--------|---------|-------|
| `showToast(message, type, duration)` | message: string, type: 'success'\|'error'\|'warning'\|'info', duration: number | Hiển thị toast tùy chỉnh |
| `success(message, duration?)` | message: string, duration?: number | Toast thành công |
| `error(message, duration?)` | message: string, duration?: number | Toast lỗi |
| `warning(message, duration?)` | message: string, duration?: number | Toast cảnh báo |
| `info(message, duration?)` | message: string, duration?: number | Toast thông tin |

### Modal Methods

| Method | Tham số | Mô tả |
|--------|---------|-------|
| `alert(title, message, onConfirm?)` | title: string, message: string, onConfirm?: function | Modal alert |
| `confirm(title, message, onConfirm?, onCancel?)` | title: string, message: string, onConfirm?: function, onCancel?: function | Modal xác nhận |
| `confirmDelete(title?, message?, onConfirm?, onCancel?)` | title?: string, message?: string, onConfirm?: function, onCancel?: function | Modal xác nhận xóa |
| `showSuccess(title?, message, onConfirm?)` | title?: string, message: string, onConfirm?: function | Modal thành công |
| `showError(title?, message, onConfirm?)` | title?: string, message: string, onConfirm?: function | Modal lỗi |
| `showWarning(title?, message, onConfirm?, onCancel?)` | title?: string, message: string, onConfirm?: function, onCancel?: function | Modal cảnh báo |

### Loading Methods

| Method | Tham số | Mô tả |
|--------|---------|-------|
| `showLoading(message?)` | message?: string | Hiển thị loading overlay |
| `hideLoading()` | - | Ẩn loading overlay |

### Error Handling Methods

| Method | Tham số | Mô tả |
|--------|---------|-------|
| `handleApiError(error, customMessage?)` | error: Error, customMessage?: string | Xử lý lỗi API |
| `handleValidationErrors(errors)` | errors: string[] \| object | Xử lý lỗi validation |
| `handleFormSubmission(submitFunction, options?)` | submitFunction: function, options?: object | Xử lý form submission với loading và error handling |

## Migration Guide

### Thay thế alert() native

```javascript
// Cũ
alert('Thông báo');

// Mới
notification.alert('Thông báo', 'Nội dung thông báo');
```

### Thay thế confirm() native

```javascript
// Cũ
if (confirm('Bạn có chắc chắn?')) {
    // Xử lý
}

// Mới
notification.confirm(
    'Xác nhận',
    'Bạn có chắc chắn?',
    () => {
        // Xử lý khi xác nhận
    }
);
```

### Thay thế error state trong component

```javascript
// Cũ
const [error, setError] = useState('');
const [loading, setLoading] = useState(false);

const handleSubmit = async () => {
    try {
        setLoading(true);
        setError('');
        await api.submit();
        alert('Thành công!');
    } catch (err) {
        setError(err.message);
    } finally {
        setLoading(false);
    }
};

// Hiển thị trong JSX
{error && <Text style={{color: 'red'}}>{error}</Text>}

// Mới
const notification = useNotification();

const handleSubmit = async () => {
    try {
        await notification.handleFormSubmission(
            () => api.submit(),
            {
                loadingMessage: 'Đang xử lý...',
                successMessage: 'Thành công!'
            }
        );
    } catch (error) {
        // Lỗi đã được xử lý tự động
    }
};
```

## Best Practices

1. **Sử dụng Toast cho feedback nhanh**: Thành công, lỗi đơn giản
2. **Sử dụng Modal cho thông báo quan trọng**: Xác nhận, cảnh báo nghiêm trọng
3. **Sử dụng handleFormSubmission cho form**: Tự động xử lý loading và error
4. **Tránh lạm dụng loading overlay**: Chỉ dùng cho các thao tác quan trọng
5. **Customize message phù hợp với ngữ cảnh**: Thông báo rõ ràng, dễ hiểu

## Troubleshooting

### Toast không hiển thị
- Kiểm tra NotificationProvider đã được wrap đúng cấp độ
- Kiểm tra z-index của các element khác

### Modal không hoạt động
- Kiểm tra callback functions có được truyền đúng không
- Kiểm tra console để xem có lỗi JavaScript không

### Loading không ẩn
- Đảm bảo luôn gọi hideLoading() trong finally block
- Sử dụng handleFormSubmission để tự động xử lý 