import { useCallback } from 'react';
import notificationService from '../utils/notificationService';

const useNotification = () => {
    // Toast notifications
    const showToast = useCallback((message, type = 'info', duration = 3000) => {
        notificationService.showToast(message, type, duration);
    }, []);

    const success = useCallback((message, duration = 3000) => {
        notificationService.success(message, duration);
    }, []);

    const error = useCallback((message, duration = 5000) => {
        notificationService.error(message, duration);
    }, []);

    const warning = useCallback((message, duration = 4000) => {
        notificationService.warning(message, duration);
    }, []);

    const info = useCallback((message, duration = 3000) => {
        notificationService.info(message, duration);
    }, []);

    // Modal notifications
    const alert = useCallback((title, message, onConfirm) => {
        notificationService.alert(title, message, onConfirm);
    }, []);

    const confirm = useCallback((title, message, onConfirm, onCancel) => {
        notificationService.confirm(title, message, onConfirm, onCancel);
    }, []);

    const confirmDelete = useCallback((title, message, onConfirm, onCancel) => {
        notificationService.confirmDelete(title, message, onConfirm, onCancel);
    }, []);

    const showSuccess = useCallback((title, message, onConfirm) => {
        notificationService.showSuccess(title, message, onConfirm);
    }, []);

    const showError = useCallback((title, message, onConfirm) => {
        notificationService.showError(title, message, onConfirm);
    }, []);

    const showWarning = useCallback((title, message, onConfirm, onCancel) => {
        notificationService.showWarning(title, message, onConfirm, onCancel);
    }, []);

    // Loading states
    const showLoading = useCallback((message = 'Đang tải...') => {
        notificationService.showLoading(message);
    }, []);

    const hideLoading = useCallback(() => {
        notificationService.hideLoading();
    }, []);

    // API Error handling
    const handleApiError = useCallback((error, customMessage = null) => {
        return notificationService.handleApiError(error, customMessage);
    }, []);

    // Validation error handling
    const handleValidationErrors = useCallback((errors) => {
        notificationService.handleValidationErrors(errors);
    }, []);

    // Form submission với error handling
    const handleFormSubmission = useCallback(async (submitFunction, options = {}) => {
        return notificationService.handleFormSubmission(submitFunction, options);
    }, []);

    return {
        // Toast methods
        showToast,
        success,
        error,
        warning,
        info,
        
        // Modal methods
        alert,
        confirm,
        confirmDelete,
        showSuccess,
        showError,
        showWarning,
        
        // Loading methods
        showLoading,
        hideLoading,
        
        // Error handling methods
        handleApiError,
        handleValidationErrors,
        handleFormSubmission
    };
};

export default useNotification; 