import React, { useEffect, useState } from 'react';
import { Box, Text } from 'zmp-ui';

const Toast = ({ toast, onRemove }) => {
    const [isVisible, setIsVisible] = useState(false);
    const [isRemoving, setIsRemoving] = useState(false);

    useEffect(() => {
        // Show animation
        const showTimer = setTimeout(() => setIsVisible(true), 100);
        
        // Auto remove
        const removeTimer = setTimeout(() => {
            handleRemove();
        }, toast.duration);

        return () => {
            clearTimeout(showTimer);
            clearTimeout(removeTimer);
        };
    }, []);

    const handleRemove = () => {
        setIsRemoving(true);
        setTimeout(() => {
            onRemove(toast.timestamp);
        }, 300);
    };

    const getToastConfig = (type) => {
        const configs = {
            success: {
                backgroundColor: '#4caf50',
                color: 'white',
                icon: '✅'
            },
            error: {
                backgroundColor: '#f44336',
                color: 'white',
                icon: '❌'
            },
            warning: {
                backgroundColor: '#ff9800',
                color: 'white',
                icon: '⚠️'
            },
            info: {
                backgroundColor: '#2196f3',
                color: 'white',
                icon: 'ℹ️'
            }
        };
        return configs[type] || configs.info;
    };

    const config = getToastConfig(toast.type);

    const toastStyle = {
        position: 'relative',
        display: 'flex',
        alignItems: 'center',
        gap: '10px',
        padding: '12px 16px',
        borderRadius: '8px',
        backgroundColor: config.backgroundColor,
        color: config.color,
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        marginBottom: '8px',
        maxWidth: '350px',
        minWidth: '250px',
        transform: isRemoving ? 'translateX(100%)' : isVisible ? 'translateX(0)' : 'translateX(100%)',
        opacity: isRemoving ? 0 : isVisible ? 1 : 0,
        transition: 'all 0.3s ease-in-out',
        cursor: 'pointer'
    };

    const iconStyle = {
        fontSize: '16px',
        flexShrink: 0
    };

    const messageStyle = {
        flex: 1,
        fontSize: '14px',
        lineHeight: '1.4'
    };

    return (
        <Box style={toastStyle} onClick={handleRemove}>
            <Text style={iconStyle}>{config.icon}</Text>
            <Text style={messageStyle}>{toast.message}</Text>
        </Box>
    );
};

const ToastContainer = ({ toasts, onRemoveToast }) => {
    const containerStyle = {
        position: 'fixed',
        top: '80px', // Below header
        right: '16px',
        zIndex: 9999,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-end',
        pointerEvents: 'none'
    };

    const toastWrapperStyle = {
        pointerEvents: 'auto'
    };

    return (
        <Box style={containerStyle}>
            {toasts.map((toast) => (
                <Box key={toast.timestamp} style={toastWrapperStyle}>
                    <Toast toast={toast} onRemove={onRemoveToast} />
                </Box>
            ))}
        </Box>
    );
};

export default ToastContainer; 