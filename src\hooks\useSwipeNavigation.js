import { useEffect, useRef, useState, useCallback } from 'react';
import { useLocation, useNavigate } from 'zmp-ui';

/**
 * Hook để xử lý swipe navigation với tr<PERSON><PERSON> nghiệm tự nhiên như Facebook/Instagram
 * @param {Object} options - Configuration options
 * @returns {Object} - Swipe state và controls
 */
const useSwipeNavigation = (options = {}) => {
    const {
        threshold = 100,
        velocityThreshold = 0.4,
        edgeThreshold = 50,
        maxEdgeStart = 20,
        resistanceDistance = 50,
        onSwipeStart = () => {},
        onSwipeMove = () => {},
        onSwipeEnd = () => {},
        onSwipeComplete = () => {},
        onSwipeCancel = () => {},
        enabled = true,
        enableHapticFeedback = true,
        enableVisualFeedback = true,
        debounceMs = 100,
        enablePreviousScreenPreview = false
    } = options;

    const location = useLocation();
    const navigate = useNavigate();

    // Touch tracking refs
    const touchStartRef = useRef(null);
    const startTimeRef = useRef(null);
    const isSwipingRef = useRef(false);
    const swipeStartedRef = useRef(false);
    const lastSwipeTimeRef = useRef(0);
    const containerRef = useRef(null);

    // Visual feedback state
    const [swipeProgress, setSwipeProgress] = useState(0);
    const [isActiveSwipe, setIsActiveSwipe] = useState(false);
    const [previousScreenVisible, setPreviousScreenVisible] = useState(false);

    // Navigation history tracking
    const historyStackRef = useRef([]);
    const [canShowPreviousScreen, setCanShowPreviousScreen] = useState(false);

    // Update history stack when location changes
    useEffect(() => {
        const currentPath = location.pathname;
        const lastPath = historyStackRef.current[historyStackRef.current.length - 1];
        
        if (currentPath !== lastPath) {
            historyStackRef.current.push(currentPath);
            
            // Keep only last 5 entries
            if (historyStackRef.current.length > 5) {
                historyStackRef.current = historyStackRef.current.slice(-5);
            }
        }
        
        setCanShowPreviousScreen(
            enablePreviousScreenPreview && 
            historyStackRef.current.length > 1 && 
            window.history.length > 1
        );
    }, [location.pathname, enablePreviousScreenPreview]);

    // Haptic feedback
    const triggerHaptic = useCallback((type = 'light') => {
        if (!enableHapticFeedback) return;
        
        try {
            if (navigator.vibrate) {
                const patterns = {
                    light: [10],
                    medium: [20],
                    heavy: [30, 20, 30]
                };
                navigator.vibrate(patterns[type] || patterns.light);
            }
        } catch (error) {
            // Haptic not supported, ignore
        }
    }, [enableHapticFeedback]);

    // Calculate swipe progress
    const calculateProgress = useCallback((deltaX, startX, screenWidth) => {
        if (startX > maxEdgeStart) return 0;
        
        let progress = Math.max(0, deltaX) / threshold;
        
        // Add resistance effect after threshold
        if (deltaX > threshold) {
            const resistanceProgress = (deltaX - threshold) / resistanceDistance;
            progress = 1 + (resistanceProgress * 0.2);
        }
        
        return Math.min(progress, 1.2);
    }, [threshold, maxEdgeStart, resistanceDistance]);

    // Check if touch started from edge
    const isEdgeStart = useCallback((x) => {
        return x <= edgeThreshold;
    }, [edgeThreshold]);

    // Touch handlers
    const handleTouchStart = useCallback((e) => {
        if (!enabled) return;
        
        const touch = e.touches[0];
        const startX = touch.clientX;
        const startY = touch.clientY;
        
        if (!isEdgeStart(startX)) return;
        
        const now = Date.now();
        if (now - lastSwipeTimeRef.current < debounceMs) return;
        
        touchStartRef.current = { x: startX, y: startY };
        startTimeRef.current = now;
        isSwipingRef.current = false;
        swipeStartedRef.current = false;
        containerRef.current = e.currentTarget;
        
        onSwipeStart({ startX, startY });
    }, [enabled, isEdgeStart, debounceMs, onSwipeStart]);

    const handleTouchMove = useCallback((e) => {
        if (!enabled || !touchStartRef.current) return;
        
        const touch = e.touches[0];
        const currentX = touch.clientX;
        const currentY = touch.clientY;
        const deltaX = currentX - touchStartRef.current.x;
        const deltaY = currentY - touchStartRef.current.y;
        
        const isHorizontalSwipe = Math.abs(deltaX) > Math.abs(deltaY);
        const isRightSwipe = deltaX > 0;
        
        if (!isHorizontalSwipe || !isRightSwipe) {
            if (isSwipingRef.current) {
                handleSwipeCancel();
            }
            return;
        }
        
        if (Math.abs(deltaX) > 10) {
            e.preventDefault();
        }
        
        if (!isSwipingRef.current && deltaX > 10) {
            isSwipingRef.current = true;
            setIsActiveSwipe(true);
            if (canShowPreviousScreen) {
                setPreviousScreenVisible(true);
            }
            triggerHaptic('light');
        }
        
        if (isSwipingRef.current) {
            const screenWidth = window.innerWidth;
            const progress = calculateProgress(deltaX, touchStartRef.current.x, screenWidth);
            
            setSwipeProgress(progress);
            
            if (!swipeStartedRef.current && progress >= 0.3) {
                swipeStartedRef.current = true;
                triggerHaptic('medium');
            }
            
            onSwipeMove({ 
                deltaX, 
                deltaY, 
                progress, 
                currentX, 
                currentY,
                isOverThreshold: deltaX >= threshold
            });
        }
    }, [enabled, calculateProgress, threshold, triggerHaptic, onSwipeMove, canShowPreviousScreen]);

    const handleTouchEnd = useCallback((e) => {
        if (!enabled || !touchStartRef.current || !isSwipingRef.current) {
            handleSwipeCancel();
            return;
        }
        
        const touch = e.changedTouches[0];
        const endX = touch.clientX;
        const endY = touch.clientY;
        const deltaX = endX - touchStartRef.current.x;
        const deltaY = endY - touchStartRef.current.y;
        const deltaTime = Date.now() - startTimeRef.current;
        
        const velocity = Math.abs(deltaX) / deltaTime;
        const isQuickSwipe = velocity >= velocityThreshold;
        const isLongSwipe = deltaX >= threshold;
        
        const shouldComplete = isLongSwipe || (isQuickSwipe && deltaX > threshold * 0.4);
        
        if (shouldComplete) {
            triggerHaptic('heavy');
            setSwipeProgress(1);
            
            setTimeout(() => {
                onSwipeComplete({
                    deltaX,
                    deltaY,
                    velocity,
                    duration: deltaTime,
                    startX: touchStartRef.current.x,
                    startY: touchStartRef.current.y,
                    endX,
                    endY
                });
                
                resetSwipeState();
            }, 150);
        } else {
            handleSwipeCancel();
        }
        
        onSwipeEnd({
            deltaX,
            deltaY,
            velocity,
            duration: deltaTime,
            completed: shouldComplete
        });
        
        lastSwipeTimeRef.current = Date.now();
    }, [enabled, threshold, velocityThreshold, triggerHaptic, onSwipeComplete, onSwipeEnd]);

    const handleSwipeCancel = useCallback(() => {
        if (isSwipingRef.current) {
            onSwipeCancel();
        }
        resetSwipeState();
    }, [onSwipeCancel]);

    const resetSwipeState = useCallback(() => {
        touchStartRef.current = null;
        startTimeRef.current = null;
        isSwipingRef.current = false;
        swipeStartedRef.current = false;
        setIsActiveSwipe(false);
        setSwipeProgress(0);
        setPreviousScreenVisible(false);
        containerRef.current = null;
    }, []);

    const handleTouchCancel = useCallback(() => {
        handleSwipeCancel();
    }, [handleSwipeCancel]);

    // Visual feedback styles
    const getSwipeStyles = useCallback(() => {
        if (!enableVisualFeedback || !isActiveSwipe) {
            return {};
        }
        
        const translateX = swipeProgress * 100;
        const opacity = Math.min(swipeProgress * 0.3, 0.3);
        
        return {
            position: 'relative',
            transform: `translateX(${Math.min(translateX, 20)}px)`,
            transition: swipeProgress >= 1 ? 'transform 0.15s ease-out' : 'none',
            '::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: `-${20 - translateX}px`,
                width: '20px',
                height: '100%',
                background: `linear-gradient(to right, transparent, rgba(0, 104, 255, ${opacity}))`,
                pointerEvents: 'none',
                zIndex: -1
            }
        };
    }, [enableVisualFeedback, isActiveSwipe, swipeProgress]);

    // Facebook-like current screen styles
    const getCurrentScreenStyles = useCallback(() => {
        if (!isActiveSwipe || !enablePreviousScreenPreview) {
            return {};
        }

        const translateX = Math.min(swipeProgress * window.innerWidth, window.innerWidth * 0.8);
        const scale = 1 - (swipeProgress * 0.05);
        
        return {
            transform: `translateX(${translateX}px) scale(${Math.max(scale, 0.95)})`,
            transition: swipeProgress >= 1 ? 'transform 0.2s ease-out' : 'none',
            zIndex: 2,
            position: 'relative'
        };
    }, [isActiveSwipe, swipeProgress, enablePreviousScreenPreview]);

    // Facebook-like previous screen styles
    const getPreviousScreenStyles = useCallback(() => {
        if (!isActiveSwipe || !enablePreviousScreenPreview || !previousScreenVisible) {
            return { display: 'none' };
        }

        const translateX = -window.innerWidth + (swipeProgress * window.innerWidth * 0.8);
        const scale = 0.9 + (swipeProgress * 0.1);
        const opacity = Math.min(swipeProgress * 1.5, 1);
        
        return {
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            transform: `translateX(${Math.max(translateX, -window.innerWidth)}px) scale(${Math.min(scale, 1)})`,
            transition: swipeProgress >= 1 ? 'transform 0.2s ease-out' : 'none',
            zIndex: 1,
            opacity,
            pointerEvents: 'none'
        };
    }, [isActiveSwipe, swipeProgress, enablePreviousScreenPreview, previousScreenVisible]);

    // Set up event listeners
    useEffect(() => {
        if (!enabled) return;
        
        const element = document.body;
        
        element.addEventListener('touchstart', handleTouchStart, { passive: true });
        element.addEventListener('touchmove', handleTouchMove, { passive: false });
        element.addEventListener('touchend', handleTouchEnd, { passive: true });
        element.addEventListener('touchcancel', handleTouchCancel, { passive: true });
        
        return () => {
            element.removeEventListener('touchstart', handleTouchStart);
            element.removeEventListener('touchmove', handleTouchMove);
            element.removeEventListener('touchend', handleTouchEnd);
            element.removeEventListener('touchcancel', handleTouchCancel);
        };
    }, [enabled, handleTouchStart, handleTouchMove, handleTouchEnd, handleTouchCancel]);

    return {
        isActiveSwipe,
        swipeProgress,
        getSwipeStyles,
        getCurrentScreenStyles,
        getPreviousScreenStyles,
        previousScreenVisible,
        canShowPreviousScreen,
        resetSwipe: resetSwipeState,
        canSwipe: enabled && !isSwipingRef.current
    };
};

export default useSwipeNavigation; 