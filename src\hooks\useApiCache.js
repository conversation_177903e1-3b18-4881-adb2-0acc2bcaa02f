import { useState, useCallback, useEffect, useRef } from 'react';

/**
 * Simple API cache hook - restored to original simple version
 * @param {Function} apiCall - Function that returns a promise
 * @param {Array} dependencies - Dependencies to trigger refetch
 * @param {Object} options - Configuration options
 * @returns {Object} - { data, loading, error, refetch }
 */
const useApiCache = (apiCall, dependencies = [], options = {}) => {
    const {
        cacheKey = '',
        enabled = true,
        cacheTime = 5 * 60 * 1000, // 5 minutes default
        onSuccess = () => {},
        onError = () => {}
    } = options;

    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const abortControllerRef = useRef(null);
    const cacheRef = useRef(new Map());
    const lastDepsRef = useRef(JSON.stringify(dependencies));

    const refetch = useCallback(async () => {
        if (!enabled || !apiCall) return;

        // Cancel previous request
        if (abortControllerRef.current) {
            abortControllerRef.current.abort();
        }

        // Create new abort controller
        abortControllerRef.current = new AbortController();

        try {
            setLoading(true);
            setError(null);

            // Check cache if cacheKey is provided
            if (cacheKey) {
                const cached = cacheRef.current.get(cacheKey);
                if (cached && Date.now() - cached.timestamp < cacheTime) {
                    setData(cached.data);
                    setLoading(false);
                    onSuccess(cached.data);
                    return cached.data;
                }
            }

            const result = await apiCall();

            // Check if request was aborted
            if (abortControllerRef.current.signal.aborted) {
                return;
            }

            setData(result);

            // Cache the result if cacheKey is provided
            if (cacheKey) {
                cacheRef.current.set(cacheKey, {
                    data: result,
                    timestamp: Date.now()
                });
            }

            onSuccess(result);
            return result;
        } catch (err) {
            if (err.name === 'AbortError') {
                return;
            }

            setError(err.message || 'An error occurred');
            onError(err);
        } finally {
            setLoading(false);
        }
    }, [apiCall, enabled, cacheKey, cacheTime, onSuccess, onError]);

    // Effect to trigger refetch when dependencies change
    useEffect(() => {
        if (!enabled) {
            return;
        }

        // Check if dependencies have actually changed
        const currentDeps = JSON.stringify(dependencies);
        if (lastDepsRef.current === currentDeps && data !== null) {
            return; // Dependencies haven't changed and we have data, skip refetch
        }

        lastDepsRef.current = currentDeps;

        // Call refetch directly to avoid dependency issues
        const fetchData = async () => {
            if (!enabled || !apiCall) return;

            // Cancel previous request
            if (abortControllerRef.current) {
                abortControllerRef.current.abort();
            }

            // Create new abort controller
            abortControllerRef.current = new AbortController();

            try {
                setLoading(true);
                setError(null);

                // Check cache if cacheKey is provided
                if (cacheKey) {
                    const cached = cacheRef.current.get(cacheKey);
                    if (cached && Date.now() - cached.timestamp < cacheTime) {
                        setData(cached.data);
                        setLoading(false);
                        onSuccess(cached.data);
                        return cached.data;
                    }
                }

                const result = await apiCall();

                // Check if request was aborted
                if (abortControllerRef.current.signal.aborted) {
                    return;
                }

                setData(result);

                // Cache the result if cacheKey is provided
                if (cacheKey) {
                    cacheRef.current.set(cacheKey, {
                        data: result,
                        timestamp: Date.now()
                    });
                }

                onSuccess(result);
                return result;
            } catch (err) {
                if (err.name === 'AbortError') {
                    return;
                }

                setError(err.message || 'An error occurred');
                onError(err);
            } finally {
                setLoading(false);
            }
        };

        fetchData();

        // Cleanup function
        return () => {
            if (abortControllerRef.current) {
                abortControllerRef.current.abort();
            }
        };
    }, [enabled, JSON.stringify(dependencies)]); // Remove refetch from dependencies

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            if (abortControllerRef.current) {
                abortControllerRef.current.abort();
            }
        };
    }, []);

    return {
        data,
        loading,
        error,
        refetch
    };
};

/**
 * Simple paginated API hook
 * @param {string} endpoint - API endpoint
 * @param {Object} options - Configuration options
 * @returns {Object} - Paginated data and controls
 */
export const usePaginatedApi = (endpoint, options = {}) => {
    const {
        pageSize = 10,
        enabled = true,
        onSuccess = () => {},
        onError = () => {}
    } = options;

    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);

    const loadMore = useCallback(async (apiCall) => {
        if (loading || !hasMore) return;

        try {
            setLoading(true);
            setError(null);

            const params = { page, limit: pageSize };
            const result = await apiCall(params);

            const newData = result.data?.data || [];

            if (page === 1) {
                setData(newData);
            } else {
                setData(prev => [...prev, ...newData]);
            }

            setHasMore(newData.length === pageSize);
            setPage(prev => prev + 1);

            onSuccess(newData, page);
        } catch (err) {
            console.error('Paginated API Error:', err);
            setError(err.message || 'An error occurred');
            onError(err);
        } finally {
            setLoading(false);
        }
    }, [loading, hasMore, page, pageSize, onSuccess, onError]);

    const refresh = useCallback((apiCall) => {
        setPage(1);
        setHasMore(true);
        setData([]);
        if (apiCall) {
            loadMore(apiCall);
        }
    }, [loadMore]);

    return {
        data,
        loading,
        error,
        hasMore,
        loadMore,
        refresh
    };
};

export default useApiCache;