import React from 'react';
import { Modal, Box, Text } from 'zmp-ui';

const ConfirmModal = ({
    visible,
    title,
    message,
    onConfirm,
    onCancel,
    confirmText = 'Xác nhận',
    cancelText = 'Hủy',
    confirmDanger = false,
    loading = false
}) => {
    // Tạo actions array dựa trên việc có cancelText hay không
    const actions = [];

    // Chỉ thêm nút Cancel nếu có cancelText
    if (cancelText) {
        actions.push({
            text: cancelText,
            close: true,
            onClick: onCancel
        });
    }

    // Luôn có nút Confirm
    actions.push({
        text: loading ? 'Đang xử lý...' : confirmText,
        close: false,
        onClick: onConfirm,
        danger: confirmDanger,
        disabled: loading
    });

    return (
        <Modal
            visible={visible}
            title={title}
            onClose={onCancel}
            actions={actions}
        >
            <Box p={4}>
                <Text style={{ lineHeight: 1.5 }}>
                    {message}
                </Text>
            </Box>
        </Modal>
    );
};

export default ConfirmModal;
