import React from 'react';
import { Modal, Box, Text } from 'zmp-ui';

const UniversalModal = ({ 
    visible, 
    options = {}, 
    onClose 
}) => {
    const {
        type = 'confirm',
        title = '',
        message = '',
        confirmText = 'Xác nhận',
        cancelText = 'Hủy',
        showCancel = true,
        confirmDanger = false,
        onConfirm = () => {},
        onCancel = () => {},
        icon = null,
        loading = false
    } = options;

    // Tạo actions array
    const actions = [];

    // Thêm nút Cancel nếu cần
    if (showCancel && cancelText) {
        actions.push({
            text: cancelText,
            close: true,
            onClick: () => {
                onCancel();
                onClose();
            },
            style: {
                backgroundColor: '#f5f5f5',
                color: '#333'
            }
        });
    }

    // Thêm nút Confirm
    actions.push({
        text: loading ? 'Đang xử lý...' : confirmText,
        close: !loading,
        onClick: () => {
            onConfirm();
            if (!loading) {
                onClose();
            }
        },
        danger: confirmDanger,
        disabled: loading,
        style: {
            backgroundColor: confirmDanger ? '#f44336' : '#0068ff',
            color: 'white'
        }
    });

    // Xác định icon hiển thị
    const getDisplayIcon = () => {
        if (icon) return icon;
        
        // Auto icon based on title or type
        if (title.toLowerCase().includes('thành công')) return '✅';
        if (title.toLowerCase().includes('lỗi')) return '❌';
        if (title.toLowerCase().includes('cảnh báo')) return '⚠️';
        if (title.toLowerCase().includes('xóa')) return '🗑️';
        if (title.toLowerCase().includes('xác nhận')) return '❓';
        if (type === 'alert') return 'ℹ️';
        
        return null;
    };

    const displayIcon = getDisplayIcon();

    return (
        <Modal
            visible={visible}
            title={title}
            onClose={() => {
                onCancel();
                onClose();
            }}
            actions={actions}
        >
            <Box style={{ padding: '20px', textAlign: 'center' }}>
                {displayIcon && (
                    <Text style={{ 
                        fontSize: '48px', 
                        marginBottom: '20px',
                        display: 'block'
                    }}>
                        {displayIcon}
                    </Text>
                )}
                <Text style={{ 
                    lineHeight: 1.5, 
                    fontSize: '16px',
                    color: '#333'
                }}>
                    {message}
                </Text>
            </Box>
        </Modal>
    );
};

export default UniversalModal; 