class NotificationService {
    constructor() {
        this.toastCallback = null;
        this.modalCallback = null;
        this.loadingCallback = null;
    }

    // Đăng ký callbacks từ các components
    registerToastCallback(callback) {
        this.toastCallback = callback;
    }

    registerModalCallback(callback) {
        this.modalCallback = callback;
    }

    registerLoadingCallback(callback) {
        this.loadingCallback = callback;
    }

    // Toast notifications
    showToast(message, type = 'info', duration = 3000) {
        if (this.toastCallback) {
            this.toastCallback({
                message,
                type, // 'success', 'error', 'warning', 'info'
                duration,
                timestamp: Date.now()
            });
        } else {
            // Fallback to console if no toast callback registered
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }

    // Các methods shorthand cho toast
    success(message, duration = 3000) {
        this.showToast(message, 'success', duration);
    }

    error(message, duration = 5000) {
        this.showToast(message, 'error', duration);
    }

    warning(message, duration = 4000) {
        this.showToast(message, 'warning', duration);
    }

    info(message, duration = 3000) {
        this.showToast(message, 'info', duration);
    }

    // Modal confirmations và alerts
    showModal(options) {
        const defaultOptions = {
            type: 'confirm', // 'alert', 'confirm', 'custom'
            title: '',
            message: '',
            confirmText: 'Xác nhận',
            cancelText: 'Hủy',
            showCancel: true,
            confirmDanger: false,
            onConfirm: () => {},
            onCancel: () => {},
            icon: null,
            loading: false
        };

        const modalOptions = { ...defaultOptions, ...options };

        if (this.modalCallback) {
            this.modalCallback(modalOptions);
        } else {
            // Fallback to native confirm/alert
            if (modalOptions.type === 'alert') {
                alert(modalOptions.message);
                modalOptions.onConfirm && modalOptions.onConfirm();
            } else {
                const result = confirm(`${modalOptions.title}\n${modalOptions.message}`);
                if (result) {
                    modalOptions.onConfirm && modalOptions.onConfirm();
                } else {
                    modalOptions.onCancel && modalOptions.onCancel();
                }
            }
        }
    }

    // Các methods shorthand cho modal
    alert(title, message, onConfirm) {
        this.showModal({
            type: 'alert',
            title,
            message,
            onConfirm,
            showCancel: false,
            confirmText: 'Đóng',
            icon: 'ℹ️'
        });
    }

    confirm(title, message, onConfirm, onCancel) {
        this.showModal({
            type: 'confirm',
            title,
            message,
            onConfirm,
            onCancel,
            icon: '❓'
        });
    }

    confirmDelete(title, message, onConfirm, onCancel) {
        this.showModal({
            type: 'confirm',
            title: title || 'Xác nhận xóa',
            message: message || 'Bạn có chắc chắn muốn xóa không? Hành động này không thể hoàn tác.',
            onConfirm,
            onCancel,
            confirmText: 'Xóa',
            confirmDanger: true,
            icon: '🗑️'
        });
    }

    showSuccess(title, message, onConfirm) {
        this.showModal({
            type: 'alert',
            title: title || 'Thành công',
            message,
            onConfirm,
            showCancel: false,
            confirmText: 'Đóng',
            icon: '✅'
        });
    }

    showError(title, message, onConfirm) {
        this.showModal({
            type: 'alert',
            title: title || 'Lỗi',
            message,
            onConfirm,
            showCancel: false,
            confirmText: 'Đóng',
            icon: '❌'
        });
    }

    showWarning(title, message, onConfirm, onCancel) {
        this.showModal({
            type: 'confirm',
            title: title || 'Cảnh báo',
            message,
            onConfirm,
            onCancel,
            confirmText: 'Tiếp tục',
            cancelText: 'Hủy',
            icon: '⚠️'
        });
    }

    // Loading states
    showLoading(message = 'Đang tải...') {
        if (this.loadingCallback) {
            this.loadingCallback({ show: true, message });
        }
    }

    hideLoading() {
        if (this.loadingCallback) {
            this.loadingCallback({ show: false });
        }
    }

    // API Error handling
    handleApiError(error, customMessage = null) {
        let errorMessage = customMessage;
        
        if (!errorMessage) {
            if (error.response) {
                // Server responded with error status
                errorMessage = error.response.data?.msg || 
                             error.response.data?.message || 
                             `Lỗi ${error.response.status}: ${error.response.statusText}`;
            } else if (error.request) {
                // Network error
                errorMessage = 'Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng.';
            } else {
                // Other error
                errorMessage = error.message || 'Có lỗi không xác định xảy ra';
            }
        }

        // Log error for debugging
        console.error('API Error:', error);

        // Show error to user
        this.error(errorMessage);
        
        return errorMessage;
    }

    // Validation error handling
    handleValidationErrors(errors) {
        if (Array.isArray(errors)) {
            const errorMessage = errors.join('\n');
            this.error(errorMessage);
        } else if (typeof errors === 'object') {
            const errorMessages = Object.values(errors).flat();
            this.error(errorMessages.join('\n'));
        } else {
            this.error(errors);
        }
    }

    // Form submission với error handling
    async handleFormSubmission(submitFunction, options = {}) {
        const {
            loadingMessage = 'Đang xử lý...',
            successMessage = 'Thao tác thành công!',
            successTitle = 'Thành công',
            onSuccess,
            onError,
            showSuccessToast = true,
            showSuccessModal = false
        } = options;

        try {
            this.showLoading(loadingMessage);
            
            const result = await submitFunction();
            
            this.hideLoading();
            
            if (showSuccessModal) {
                this.showSuccess(successTitle, successMessage, onSuccess);
            } else if (showSuccessToast) {
                this.success(successMessage);
                onSuccess && onSuccess(result);
            }
            
            return result;
        } catch (error) {
            this.hideLoading();
            
            const errorMessage = this.handleApiError(error);
            onError && onError(error, errorMessage);
            
            throw error;
        }
    }
}

// Tạo instance singleton
const notificationService = new NotificationService();

export default notificationService; 