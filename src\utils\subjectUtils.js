// src/utils/subjectUtils.js

/**
 * Subject utilities for icons and styling
 */

// Subject icon mapping
const SUBJECT_ICONS = {
    // Math subjects
    'Toán': '🔢',
    'To<PERSON> học': '🔢',
    'Đại số': '📐',
    '<PERSON><PERSON><PERSON> học': '📐',
    'Giải tích': '📊',
    
    // Language subjects
    'Ngữ văn': '📝',
    '<PERSON>ă<PERSON> học': '📚',
    'Tiếng Anh': '🇬🇧',
    'Tiếng Pháp': '🇫🇷',
    'Tiếng Nhật': '🇯🇵',
    'Tiếng Trung': '🇨🇳',
    
    // Science subjects
    'Vật lý': '⚛️',
    'Hóa học': '🧪',
    '<PERSON>h học': '🧬',
    '<PERSON><PERSON>a học tự nhiên': '🔬',
    
    // Social studies
    'Lịch sử': '📚',
    'Địa lý': '🌍',
    'GDCD': '⚖️',
    'Giáo dục công dân': '⚖️',
    '<PERSON><PERSON> hội học': '👥',
    
    // Arts and physical
    'Thể dục': '🏃‍♂️',
    '<PERSON>m nhạc': '🎵',
    '<PERSON><PERSON> thuật': '🎨',
    'Hội họa': '🖼️',
    
    // Technology
    'Tin học': '💻',
    'Công nghệ thông tin': '💻',
    'Kỹ thuật': '⚙️',
    'Công nghệ': '🔧',
    
    // Default
    'default': '📖'
};

// Subject icon class mapping for CSS styling
const SUBJECT_ICON_CLASSES = {
    'Toán': 'subject-math',
    'Toán học': 'subject-math',
    'Đại số': 'subject-math',
    'Hình học': 'subject-math',
    'Giải tích': 'subject-math',
    
    'Ngữ văn': 'subject-literature',
    'Văn học': 'subject-literature',
    'Tiếng Anh': 'subject-language',
    'Tiếng Pháp': 'subject-language',
    'Tiếng Nhật': 'subject-language',
    'Tiếng Trung': 'subject-language',
    
    'Vật lý': 'subject-science',
    'Hóa học': 'subject-science',
    'Sinh học': 'subject-science',
    'Khoa học tự nhiên': 'subject-science',
    
    'Lịch sử': 'subject-social',
    'Địa lý': 'subject-social',
    'GDCD': 'subject-social',
    'Giáo dục công dân': 'subject-social',
    'Xã hội học': 'subject-social',
    
    'Thể dục': 'subject-physical',
    'Âm nhạc': 'subject-arts',
    'Mỹ thuật': 'subject-arts',
    'Hội họa': 'subject-arts',
    
    'Tin học': 'subject-tech',
    'Công nghệ thông tin': 'subject-tech',
    'Kỹ thuật': 'subject-tech',
    'Công nghệ': 'subject-tech',
    
    'default': 'subject-default'
};

// Subject color mapping
const SUBJECT_COLORS = {
    'subject-math': '#007bff',
    'subject-literature': '#28a745',
    'subject-language': '#6f42c1',
    'subject-science': '#fd7e14',
    'subject-social': '#dc3545',
    'subject-physical': '#20c997',
    'subject-arts': '#e83e8c',
    'subject-tech': '#6c757d',
    'subject-default': '#343a40'
};

/**
 * Get icon for a subject
 * @param {string} subjectName - Name of the subject
 * @returns {string} - Emoji icon for the subject
 */
export const getSubjectIcon = (subjectName) => {
    if (!subjectName) return SUBJECT_ICONS.default;
    
    // Normalize subject name for matching
    const normalizedName = subjectName.trim();
    
    // Direct match first
    if (SUBJECT_ICONS[normalizedName]) {
        return SUBJECT_ICONS[normalizedName];
    }
    
    // Partial match for subjects with similar names
    const subjectKeys = Object.keys(SUBJECT_ICONS);
    const matchedKey = subjectKeys.find(key => 
        normalizedName.toLowerCase().includes(key.toLowerCase()) ||
        key.toLowerCase().includes(normalizedName.toLowerCase())
    );
    
    return SUBJECT_ICONS[matchedKey] || SUBJECT_ICONS.default;
};

/**
 * Get CSS class for a subject icon
 * @param {string} subjectName - Name of the subject
 * @returns {string} - CSS class for styling
 */
export const getSubjectIconClass = (subjectName) => {
    if (!subjectName) return SUBJECT_ICON_CLASSES.default;
    
    // Normalize subject name for matching
    const normalizedName = subjectName.trim();
    
    // Direct match first
    if (SUBJECT_ICON_CLASSES[normalizedName]) {
        return SUBJECT_ICON_CLASSES[normalizedName];
    }
    
    // Partial match for subjects with similar names
    const subjectKeys = Object.keys(SUBJECT_ICON_CLASSES);
    const matchedKey = subjectKeys.find(key => 
        normalizedName.toLowerCase().includes(key.toLowerCase()) ||
        key.toLowerCase().includes(normalizedName.toLowerCase())
    );
    
    return SUBJECT_ICON_CLASSES[matchedKey] || SUBJECT_ICON_CLASSES.default;
};

/**
 * Get color for a subject
 * @param {string} subjectName - Name of the subject
 * @returns {string} - Hex color code
 */
export const getSubjectColor = (subjectName) => {
    const className = getSubjectIconClass(subjectName);
    return SUBJECT_COLORS[className] || SUBJECT_COLORS['subject-default'];
};

/**
 * Get subject category
 * @param {string} subjectName - Name of the subject
 * @returns {string} - Category name
 */
export const getSubjectCategory = (subjectName) => {
    const className = getSubjectIconClass(subjectName);
    
    const categoryMap = {
        'subject-math': 'Toán học',
        'subject-literature': 'Ngữ văn',
        'subject-language': 'Ngoại ngữ',
        'subject-science': 'Khoa học tự nhiên',
        'subject-social': 'Khoa học xã hội',
        'subject-physical': 'Thể chất',
        'subject-arts': 'Nghệ thuật',
        'subject-tech': 'Công nghệ',
        'subject-default': 'Khác'
    };
    
    return categoryMap[className] || categoryMap['subject-default'];
};

/**
 * Format subject name for display
 * @param {string} subjectName - Name of the subject
 * @returns {string} - Formatted subject name
 */
export const formatSubjectName = (subjectName) => {
    if (!subjectName) return 'Chưa xác định';
    
    // Capitalize first letter of each word
    return subjectName
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(' ');
};

/**
 * Check if subject is core subject (required)
 * @param {string} subjectName - Name of the subject
 * @returns {boolean} - True if core subject
 */
export const isCoreSubject = (subjectName) => {
    const coreSubjects = [
        'Toán', 'Toán học',
        'Ngữ văn', 'Văn học', 
        'Tiếng Anh',
        'Vật lý', 'Hóa học', 'Sinh học',
        'Lịch sử', 'Địa lý', 'GDCD'
    ];
    
    return coreSubjects.some(core => 
        subjectName.toLowerCase().includes(core.toLowerCase())
    );
};

export default {
    getSubjectIcon,
    getSubjectIconClass,
    getSubjectColor,
    getSubjectCategory,
    formatSubjectName,
    isCoreSubject
};
